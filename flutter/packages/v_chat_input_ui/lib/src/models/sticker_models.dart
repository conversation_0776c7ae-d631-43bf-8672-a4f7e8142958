// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

/// Represents a single sticker
class VSticker {
  final String id;
  final String name;
  final String assetPath;
  final String? emoji; // Optional emoji representation
  final List<String> tags; // For search functionality

  const VSticker({
    required this.id,
    required this.name,
    required this.assetPath,
    this.emoji,
    this.tags = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'assetPath': assetPath,
      'emoji': emoji,
      'tags': tags,
    };
  }

  factory VSticker.fromJson(Map<String, dynamic> json) {
    return VSticker(
      id: json['id'] as String,
      name: json['name'] as String,
      assetPath: json['assetPath'] as String,
      emoji: json['emoji'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }
}

/// Represents a sticker pack/category
class VStickerPack {
  final String id;
  final String name;
  final String iconPath;
  final List<VSticker> stickers;
  final bool isDefault;

  const VStickerPack({
    required this.id,
    required this.name,
    required this.iconPath,
    required this.stickers,
    this.isDefault = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'iconPath': iconPath,
      'stickers': stickers.map((s) => s.toJson()).toList(),
      'isDefault': isDefault,
    };
  }

  factory VStickerPack.fromJson(Map<String, dynamic> json) {
    return VStickerPack(
      id: json['id'] as String,
      name: json['name'] as String,
      iconPath: json['iconPath'] as String,
      stickers: (json['stickers'] as List<dynamic>)
          .map((s) => VSticker.fromJson(s as Map<String, dynamic>))
          .toList(),
      isDefault: json['isDefault'] as bool? ?? false,
    );
  }
}

/// Sticker message data for sending stickers
class VStickerMessageData {
  final String stickerId;
  final String stickerPackId;
  final String assetPath;
  final String name;

  const VStickerMessageData({
    required this.stickerId,
    required this.stickerPackId,
    required this.assetPath,
    required this.name,
  });

  Map<String, dynamic> toJson() {
    return {
      'stickerId': stickerId,
      'stickerPackId': stickerPackId,
      'assetPath': assetPath,
      'name': name,
    };
  }

  factory VStickerMessageData.fromJson(Map<String, dynamic> json) {
    return VStickerMessageData(
      stickerId: json['stickerId'] as String,
      stickerPackId: json['stickerPackId'] as String,
      assetPath: json['assetPath'] as String,
      name: json['name'] as String,
    );
  }
}
