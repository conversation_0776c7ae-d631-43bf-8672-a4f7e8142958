// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../models/sticker_models.dart';
import 'sticker_data.dart';

/// Manager for dynamic sticker packs (downloaded, user-created, etc.)
class DynamicStickerManager {
  static const String _stickerDir = 'stickers';
  
  /// Get all sticker packs (default + dynamic)
  static Future<List<VStickerPack>> getAllStickerPacks() async {
    final defaultPacks = VStickerData.getDefaultStickerPacks();
    final dynamicPacks = await getDynamicStickerPacks();
    
    return [...defaultPacks, ...dynamicPacks];
  }
  
  /// Get dynamic sticker packs from local storage
  static Future<List<VStickerPack>> getDynamicStickerPacks() async {
    if (kIsWeb) {
      // For web, you might want to use IndexedDB or localStorage
      return [];
    }
    
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final stickerDir = Directory('${appDir.path}/$_stickerDir');
      
      if (!await stickerDir.exists()) {
        return [];
      }
      
      final packs = <VStickerPack>[];
      
      await for (final entity in stickerDir.list()) {
        if (entity is Directory) {
          final pack = await _loadStickerPackFromDirectory(entity);
          if (pack != null) {
            packs.add(pack);
          }
        }
      }
      
      return packs;
    } catch (e) {
      print('Error loading dynamic sticker packs: $e');
      return [];
    }
  }
  
  /// Load a sticker pack from a directory
  static Future<VStickerPack?> _loadStickerPackFromDirectory(Directory dir) async {
    try {
      final packName = dir.path.split('/').last;
      final stickers = <VSticker>[];
      String? iconPath;
      
      await for (final file in dir.list()) {
        if (file is File && _isImageFile(file.path)) {
          final fileName = file.path.split('/').last;
          final stickerName = fileName.split('.').first;
          
          if (fileName.startsWith('icon.')) {
            iconPath = file.path;
          } else {
            stickers.add(VSticker(
              id: '${packName}_$stickerName',
              name: stickerName,
              assetPath: file.path,
              tags: [packName, stickerName],
            ));
          }
        }
      }
      
      if (stickers.isEmpty) return null;
      
      return VStickerPack(
        id: packName,
        name: packName,
        iconPath: iconPath ?? stickers.first.assetPath,
        stickers: stickers,
      );
    } catch (e) {
      print('Error loading sticker pack from ${dir.path}: $e');
      return null;
    }
  }
  
  /// Check if file is an image
  static bool _isImageFile(String path) {
    final ext = path.toLowerCase().split('.').last;
    return ['png', 'jpg', 'jpeg', 'gif', 'webp'].contains(ext);
  }
  
  /// Add a new sticker pack from files
  static Future<bool> addStickerPack({
    required String packName,
    required List<File> stickerFiles,
    File? iconFile,
  }) async {
    if (kIsWeb) {
      // For web, implement using IndexedDB or similar
      return false;
    }
    
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final packDir = Directory('${appDir.path}/$_stickerDir/$packName');
      
      if (await packDir.exists()) {
        await packDir.delete(recursive: true);
      }
      
      await packDir.create(recursive: true);
      
      // Copy icon file
      if (iconFile != null) {
        final iconExt = iconFile.path.split('.').last;
        await iconFile.copy('${packDir.path}/icon.$iconExt');
      }
      
      // Copy sticker files
      for (int i = 0; i < stickerFiles.length; i++) {
        final file = stickerFiles[i];
        final ext = file.path.split('.').last;
        await file.copy('${packDir.path}/sticker_$i.$ext');
      }
      
      return true;
    } catch (e) {
      print('Error adding sticker pack: $e');
      return false;
    }
  }
  
  /// Remove a sticker pack
  static Future<bool> removeStickerPack(String packId) async {
    if (kIsWeb) {
      return false;
    }
    
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final packDir = Directory('${appDir.path}/$_stickerDir/$packId');
      
      if (await packDir.exists()) {
        await packDir.delete(recursive: true);
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error removing sticker pack: $e');
      return false;
    }
  }
  
  /// Download and install a sticker pack from URL
  static Future<bool> downloadStickerPack({
    required String packName,
    required List<String> stickerUrls,
    String? iconUrl,
  }) async {
    // Implementation for downloading stickers from URLs
    // This would involve HTTP requests and file downloads
    // For now, this is a placeholder
    return false;
  }
}
