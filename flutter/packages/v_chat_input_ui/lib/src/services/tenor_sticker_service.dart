// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/sticker_models.dart';

/// Service for fetching stickers from Tenor API
class TenorStickerService {
  static const String _baseUrl = 'https://tenor.googleapis.com/v2';
  static const String _apiKey = 'YOUR_TENOR_API_KEY'; // Replace with your API key
  
  /// Search for stickers by query
  static Future<List<VSticker>> searchStickers({
    required String query,
    int limit = 20,
    String? pos, // Position for pagination
    String contentFilter = 'high', // high, medium, low, off
    String mediaFilter = 'sticker', // sticker, gif, or both
  }) async {
    try {
      final queryParams = {
        'key': _apiKey,
        'q': query,
        'limit': limit.toString(),
        'contentfilter': contentFilter,
        'media_filter': mediaFilter,
        'client_key': 'flutter_chat_app',
      };
      
      if (pos != null) {
        queryParams['pos'] = pos;
      }

      final url = Uri.parse('$_baseUrl/search').replace(queryParameters: queryParams);
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> results = data['results'] ?? [];
        
        return results.map((result) => _mapTenorToSticker(result)).toList();
      } else {
        throw Exception('Failed to search stickers: ${response.statusCode}');
      }
    } catch (e) {
      print('Error searching stickers: $e');
      return [];
    }
  }

  /// Get featured/trending stickers
  static Future<List<VSticker>> getFeaturedStickers({
    int limit = 20,
    String? pos,
    String contentFilter = 'high',
  }) async {
    try {
      final queryParams = {
        'key': _apiKey,
        'limit': limit.toString(),
        'contentfilter': contentFilter,
        'media_filter': 'sticker',
        'client_key': 'flutter_chat_app',
      };
      
      if (pos != null) {
        queryParams['pos'] = pos;
      }

      final url = Uri.parse('$_baseUrl/featured').replace(queryParameters: queryParams);
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> results = data['results'] ?? [];
        
        return results.map((result) => _mapTenorToSticker(result)).toList();
      } else {
        throw Exception('Failed to get featured stickers: ${response.statusCode}');
      }
    } catch (e) {
      print('Error getting featured stickers: $e');
      return [];
    }
  }

  /// Get trending search terms
  static Future<List<String>> getTrendingTerms({
    int limit = 10,
  }) async {
    try {
      final url = Uri.parse('$_baseUrl/trending_terms').replace(queryParameters: {
        'key': _apiKey,
        'limit': limit.toString(),
        'client_key': 'flutter_chat_app',
      });

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> results = data['results'] ?? [];
        
        return results.cast<String>();
      } else {
        throw Exception('Failed to get trending terms: ${response.statusCode}');
      }
    } catch (e) {
      print('Error getting trending terms: $e');
      return [];
    }
  }

  /// Get autocomplete suggestions
  static Future<List<String>> getAutocompleteSuggestions({
    required String query,
    int limit = 10,
  }) async {
    try {
      final url = Uri.parse('$_baseUrl/autocomplete').replace(queryParameters: {
        'key': _apiKey,
        'q': query,
        'limit': limit.toString(),
        'client_key': 'flutter_chat_app',
      });

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> results = data['results'] ?? [];
        
        return results.cast<String>();
      } else {
        throw Exception('Failed to get autocomplete: ${response.statusCode}');
      }
    } catch (e) {
      print('Error getting autocomplete: $e');
      return [];
    }
  }

  /// Map Tenor response to VSticker
  static VSticker _mapTenorToSticker(Map<String, dynamic> result) {
    final id = result['id'] as String;
    final title = result['content_description'] as String? ?? 'Sticker';
    final tags = (result['tags'] as List?)?.cast<String>() ?? [];
    
    // Get media formats
    final mediaFormats = result['media_formats'] as Map<String, dynamic>? ?? {};
    
    // Prefer sticker format, fallback to gif
    final stickerFormat = mediaFormats['webp'] as Map<String, dynamic>? ??
                         mediaFormats['gif'] as Map<String, dynamic>? ??
                         {};
    
    final imageUrl = stickerFormat['url'] as String? ?? '';
    
    return VSticker(
      id: 'tenor_$id',
      name: title,
      assetPath: imageUrl, // This will be a URL
      tags: [...tags, ...title.toLowerCase().split(' ')],
    );
  }

  /// Register a share event (Tenor analytics)
  static Future<void> registerShare(String stickerId) async {
    try {
      final url = Uri.parse('$_baseUrl/registershare').replace(queryParameters: {
        'key': _apiKey,
        'id': stickerId.replaceFirst('tenor_', ''),
        'client_key': 'flutter_chat_app',
      });

      await http.get(url);
    } catch (e) {
      print('Error registering share: $e');
    }
  }
}
