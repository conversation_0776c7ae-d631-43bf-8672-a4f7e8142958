// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/sticker_models.dart';

/// Service for fetching stickers from GIPHY API
class GiphyStickerService {
  static const String _baseUrl = 'https://api.giphy.com/v1/stickers';
  static const String _apiKey = 'WrQBFFR2h4qDmI82d4wLVOLQdn1JfcBP';

  // Simple cache to avoid hitting rate limits
  static final Map<String, List<VSticker>> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // Rate limiting
  static DateTime? _lastRequestTime;
  static const Duration _minRequestInterval = Duration(milliseconds: 500);

  /// Search for stickers by query
  static Future<List<VSticker>> searchStickers({
    required String query,
    int limit = 25,
    int offset = 0,
    String rating = 'g', // g, pg, pg-13, r
  }) async {
    // Create cache key
    final cacheKey = 'search_${query}_${limit}_${offset}_$rating';

    // Check cache first
    if (_cache.containsKey(cacheKey) &&
        _cacheTimestamps.containsKey(cacheKey)) {
      final cacheTime = _cacheTimestamps[cacheKey]!;
      if (DateTime.now().difference(cacheTime) < _cacheExpiry) {
        print('Returning cached stickers for: $query');
        return _cache[cacheKey]!;
      }
    }

    // Rate limiting - wait if needed
    if (_lastRequestTime != null) {
      final timeSinceLastRequest = DateTime.now().difference(_lastRequestTime!);
      if (timeSinceLastRequest < _minRequestInterval) {
        final waitTime = _minRequestInterval - timeSinceLastRequest;
        await Future.delayed(waitTime);
      }
    }

    try {
      _lastRequestTime = DateTime.now();

      final url = Uri.parse('$_baseUrl/search').replace(queryParameters: {
        'api_key': _apiKey,
        'q': query,
        'limit': limit.toString(),
        'offset': offset.toString(),
        'rating': rating,
        'lang': 'en',
      });

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> gifs = data['data'] ?? [];

        final stickers = gifs.map((gif) => _mapGiphyToSticker(gif)).toList();

        // Cache the results
        _cache[cacheKey] = stickers;
        _cacheTimestamps[cacheKey] = DateTime.now();

        return stickers;
      } else {
        throw Exception('Failed to search stickers: ${response.statusCode}');
      }
    } catch (e) {
      print('Error searching stickers: $e');
      return [];
    }
  }

  /// Get trending stickers
  static Future<List<VSticker>> getTrendingStickers({
    int limit = 25,
    int offset = 0,
    String rating = 'g',
  }) async {
    // Create cache key
    final cacheKey = 'trending_${limit}_${offset}_$rating';

    // Check cache first
    if (_cache.containsKey(cacheKey) &&
        _cacheTimestamps.containsKey(cacheKey)) {
      final cacheTime = _cacheTimestamps[cacheKey]!;
      if (DateTime.now().difference(cacheTime) < _cacheExpiry) {
        print('Returning cached trending stickers');
        return _cache[cacheKey]!;
      }
    }

    // Rate limiting - wait if needed
    if (_lastRequestTime != null) {
      final timeSinceLastRequest = DateTime.now().difference(_lastRequestTime!);
      if (timeSinceLastRequest < _minRequestInterval) {
        final waitTime = _minRequestInterval - timeSinceLastRequest;
        await Future.delayed(waitTime);
      }
    }

    try {
      _lastRequestTime = DateTime.now();

      final url = Uri.parse('$_baseUrl/trending').replace(queryParameters: {
        'api_key': _apiKey,
        'limit': limit.toString(),
        'offset': offset.toString(),
        'rating': rating,
      });

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> gifs = data['data'] ?? [];

        final stickers = gifs.map((gif) => _mapGiphyToSticker(gif)).toList();

        // Cache the results
        _cache[cacheKey] = stickers;
        _cacheTimestamps[cacheKey] = DateTime.now();

        return stickers;
      } else {
        throw Exception(
            'Failed to get trending stickers: ${response.statusCode}');
      }
    } catch (e) {
      print('Error getting trending stickers: $e');
      return [];
    }
  }

  /// Get sticker categories
  static Future<List<String>> getCategories() async {
    try {
      final url = Uri.parse('$_baseUrl/categories').replace(queryParameters: {
        'api_key': _apiKey,
      });

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> categories = data['data'] ?? [];

        return categories.map((cat) => cat['name'] as String).toList();
      } else {
        throw Exception('Failed to get categories: ${response.statusCode}');
      }
    } catch (e) {
      print('Error getting categories: $e');
      return [];
    }
  }

  /// Get stickers by category
  static Future<List<VSticker>> getStickersByCategory({
    required String category,
    int limit = 25,
    int offset = 0,
  }) async {
    return searchStickers(
      query: category,
      limit: limit,
      offset: offset,
    );
  }

  /// Map GIPHY response to VSticker
  static VSticker _mapGiphyToSticker(Map<String, dynamic> gif) {
    final id = gif['id'] as String;
    final title = gif['title'] as String? ?? 'Sticker';
    final images = gif['images'] as Map<String, dynamic>? ?? {};

    // Get different image sizes
    final original = images['original'] as Map<String, dynamic>? ?? {};
    final fixedHeight = images['fixed_height'] as Map<String, dynamic>? ?? {};
    final preview = images['preview_gif'] as Map<String, dynamic>? ?? {};

    // Use fixed_height for better performance, fallback to original
    final imageUrl =
        (fixedHeight['url'] as String?) ?? (original['url'] as String?) ?? '';

    // Extract tags for search
    final tags = <String>[];
    if (gif['tags'] != null) {
      tags.addAll((gif['tags'] as List).cast<String>());
    }

    // Add title words as tags
    tags.addAll(title.toLowerCase().split(' '));

    return VSticker(
      id: 'giphy_$id',
      name: title,
      assetPath: imageUrl, // This will be a URL instead of asset path
      tags: tags,
    );
  }

  /// Download and cache sticker locally (optional)
  static Future<String?> downloadSticker(String url, String stickerId) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        // Here you would save the file locally and return the local path
        // For now, just return the URL
        return url;
      }
    } catch (e) {
      print('Error downloading sticker: $e');
    }
    return null;
  }
}
