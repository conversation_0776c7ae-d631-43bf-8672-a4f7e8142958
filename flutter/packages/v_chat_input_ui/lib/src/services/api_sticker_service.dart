// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import '../models/sticker_models.dart';
import 'giphy_sticker_service.dart';
import 'tenor_sticker_service.dart';

/// Available sticker providers
enum StickerProvider { giphy, tenor }

/// Unified service for fetching stickers from various APIs
class ApiStickerService {
  /// Current provider (can be changed at runtime)
  static StickerProvider currentProvider = StickerProvider.giphy;

  /// Search for stickers across all providers or specific provider
  static Future<List<VSticker>> searchStickers({
    required String query,
    int limit = 25,
    StickerProvider? provider,
  }) async {
    final targetProvider = provider ?? currentProvider;

    switch (targetProvider) {
      case StickerProvider.giphy:
        return GiphyStickerService.searchStickers(
          query: query,
          limit: limit,
        );
      case StickerProvider.tenor:
        return TenorStickerService.searchStickers(
          query: query,
          limit: limit,
        );
    }
  }

  /// Get trending/featured stickers
  static Future<List<VSticker>> getTrendingStickers({
    int limit = 25,
    StickerProvider? provider,
  }) async {
    final targetProvider = provider ?? currentProvider;

    switch (targetProvider) {
      case StickerProvider.giphy:
        return GiphyStickerService.getTrendingStickers(limit: limit);
      case StickerProvider.tenor:
        return TenorStickerService.getFeaturedStickers(limit: limit);
    }
  }

  /// Get trending search terms
  static Future<List<String>> getTrendingTerms({
    StickerProvider? provider,
  }) async {
    final targetProvider = provider ?? currentProvider;

    switch (targetProvider) {
      case StickerProvider.giphy:
        return GiphyStickerService.getCategories();
      case StickerProvider.tenor:
        return TenorStickerService.getTrendingTerms();
    }
  }

  /// Get autocomplete suggestions
  static Future<List<String>> getAutocompleteSuggestions({
    required String query,
    StickerProvider? provider,
  }) async {
    final targetProvider = provider ?? currentProvider;

    switch (targetProvider) {
      case StickerProvider.giphy:
        // GIPHY doesn't have autocomplete, return empty
        return [];
      case StickerProvider.tenor:
        return TenorStickerService.getAutocompleteSuggestions(query: query);
    }
  }

  /// Create sticker packs from API results
  static List<VStickerPack> createStickerPacksFromResults({
    required List<VSticker> trending,
    required Map<String, List<VSticker>> categorized,
  }) {
    final packs = <VStickerPack>[];

    // Add trending pack
    if (trending.isNotEmpty) {
      packs.add(VStickerPack(
        id: 'trending',
        name: 'Trending',
        iconPath: trending.first.assetPath,
        stickers: trending,
      ));
    }

    // Add categorized packs
    categorized.forEach((category, stickers) {
      if (stickers.isNotEmpty) {
        packs.add(VStickerPack(
          id: 'api_$category',
          name: category,
          iconPath: stickers.first.assetPath,
          stickers: stickers,
        ));
      }
    });

    return packs;
  }

  /// Load popular categories with stickers
  static Future<Map<String, List<VSticker>>> loadPopularCategories({
    List<String>? categories,
    int stickersPerCategory = 10,
    StickerProvider? provider,
  }) async {
    final targetCategories = categories ??
        [
          'happy',
          'sad',
          'love',
          'angry',
          'surprised',
          'funny',
          'cute',
          'cool',
          'party',
          'thumbs up'
        ];

    final results = <String, List<VSticker>>{};

    for (final category in targetCategories) {
      try {
        final stickers = await searchStickers(
          query: category,
          limit: stickersPerCategory,
          provider: provider,
        );
        if (stickers.isNotEmpty) {
          results[category] = stickers;
        }
      } catch (e) {
        print('Error loading category $category: $e');
      }
    }

    return results;
  }

  /// Register sticker usage (for analytics)
  static Future<void> registerStickerUsage(VSticker sticker) async {
    if (sticker.id.startsWith('tenor_')) {
      await TenorStickerService.registerShare(sticker.id);
    }
    // Add other provider analytics here
  }
}
