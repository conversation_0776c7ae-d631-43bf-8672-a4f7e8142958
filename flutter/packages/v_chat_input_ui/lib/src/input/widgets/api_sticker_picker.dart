// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import '../../models/sticker_models.dart';
import '../../services/api_sticker_service.dart';
import '../../widgets/network_sticker_widget.dart';
import 'emoji_sticker_keyboard.dart';

/// Enhanced sticker picker with API integration
class ApiStickerPicker extends StatefulWidget {
  final OnStickerSelected onStickerSelected;
  final double height;

  const ApiStickerPicker({
    super.key,
    required this.onStickerSelected,
    this.height = 300,
  });

  @override
  State<ApiStickerPicker> createState() => _ApiStickerPickerState();
}

class _ApiStickerPickerState extends State<ApiStickerPicker>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  
  List<VSticker> _trendingStickers = [];
  List<VSticker> _searchResults = [];
  Map<String, List<VSticker>> _categories = {};
  List<String> _trendingTerms = [];
  
  bool _isLoading = true;
  bool _isSearching = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadInitialData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    if (query != _currentQuery) {
      _currentQuery = query;
      if (query.isEmpty) {
        setState(() {
          _searchResults.clear();
          _isSearching = false;
        });
      } else {
        _searchStickers(query);
      }
    }
  }

  Future<void> _loadInitialData() async {
    setState(() => _isLoading = true);
    
    try {
      // Load trending stickers and terms in parallel
      final futures = await Future.wait([
        ApiStickerService.getTrendingStickers(limit: 30),
        ApiStickerService.getTrendingTerms(),
        ApiStickerService.loadPopularCategories(stickersPerCategory: 15),
      ]);
      
      if (mounted) {
        setState(() {
          _trendingStickers = futures[0] as List<VSticker>;
          _trendingTerms = futures[1] as List<String>;
          _categories = futures[2] as Map<String, List<VSticker>>;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading initial data: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _searchStickers(String query) async {
    setState(() => _isSearching = true);
    
    try {
      final results = await ApiStickerService.searchStickers(
        query: query,
        limit: 50,
      );
      
      if (mounted && _currentQuery == query) {
        setState(() {
          _searchResults = results;
          _isSearching = false;
        });
      }
    } catch (e) {
      print('Error searching stickers: $e');
      if (mounted) {
        setState(() => _isSearching = false);
      }
    }
  }

  void _onStickerTap(VSticker sticker) {
    // Register usage for analytics
    ApiStickerService.registerStickerUsage(sticker);
    widget.onStickerSelected(sticker);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300, width: 0.5),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(8),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search stickers...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _isSearching
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () => _searchController.clear(),
                          )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              ),
            ),
          ),
          
          // Tabs
          if (!_isSearching && _searchResults.isEmpty)
            TabBar(
              controller: _tabController,
              indicatorColor: Theme.of(context).primaryColor,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey.shade600,
              tabs: const [
                Tab(text: 'Trending'),
                Tab(text: 'Categories'),
                Tab(text: 'Recents'),
              ],
            ),
          
          // Content
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (_searchResults.isNotEmpty || _isSearching) {
      return _buildSearchResults();
    }
    
    return TabBarView(
      controller: _tabController,
      children: [
        _buildTrendingTab(),
        _buildCategoriesTab(),
        _buildRecentsTab(),
      ],
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty && !_isSearching) {
      return const Center(
        child: Text('No stickers found'),
      );
    }
    
    return _buildStickerGrid(_searchResults);
  }

  Widget _buildTrendingTab() {
    if (_trendingStickers.isEmpty) {
      return const Center(child: Text('No trending stickers'));
    }
    
    return Column(
      children: [
        // Trending terms chips
        if (_trendingTerms.isNotEmpty)
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _trendingTerms.length,
              itemBuilder: (context, index) {
                final term = _trendingTerms[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ActionChip(
                    label: Text(term),
                    onPressed: () {
                      _searchController.text = term;
                    },
                  ),
                );
              },
            ),
          ),
        
        // Trending stickers grid
        Expanded(
          child: _buildStickerGrid(_trendingStickers),
        ),
      ],
    );
  }

  Widget _buildCategoriesTab() {
    if (_categories.isEmpty) {
      return const Center(child: Text('No categories available'));
    }
    
    return ListView.builder(
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories.keys.elementAt(index);
        final stickers = _categories[category]!;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                category.toUpperCase(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                itemCount: stickers.length,
                itemBuilder: (context, stickerIndex) {
                  final sticker = stickers[stickerIndex];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: NetworkStickerWidget(
                      sticker: sticker,
                      size: 80,
                      onTap: () => _onStickerTap(sticker),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecentsTab() {
    // TODO: Implement recent stickers from local storage
    return const Center(
      child: Text('Recent stickers will appear here'),
    );
  }

  Widget _buildStickerGrid(List<VSticker> stickers) {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: stickers.length,
      itemBuilder: (context, index) {
        final sticker = stickers[index];
        return NetworkStickerWidget(
          sticker: sticker,
          size: 80,
          onTap: () => _onStickerTap(sticker),
        );
      },
    );
  }
}
