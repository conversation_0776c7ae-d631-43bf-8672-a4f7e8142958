// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import '../../data/dynamic_sticker_manager.dart';
import '../../models/sticker_models.dart';
import '../../widgets/sticker_placeholder.dart';
import 'emoji_sticker_keyboard.dart';

/// Sticker picker widget that displays sticker packs and individual stickers
class StickerPicker extends StatefulWidget {
  final OnStickerSelected onStickerSelected;
  final double height;

  const StickerPicker({
    super.key,
    required this.onStickerSelected,
    this.height = 250,
  });

  @override
  State<StickerPicker> createState() => _StickerPickerState();
}

class _StickerPickerState extends State<StickerPicker>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  List<VStickerPack> _stickerPacks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStickerPacks();
  }

  Future<void> _loadStickerPacks() async {
    final packs = await DynamicStickerManager.getAllStickerPacks();
    if (mounted) {
      setState(() {
        _stickerPacks = packs;
        _isLoading = false;
        _tabController = TabController(
          length: _stickerPacks.length,
          vsync: this,
        );
      });
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _tabController == null) {
      return Container(
        height: widget.height,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
          ),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade300,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          // Tab bar for sticker packs
          Container(
            height: 50,
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade300,
                  width: 0.5,
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              indicatorColor: Theme.of(context).primaryColor,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey.shade600,
              tabs: _stickerPacks.map((pack) {
                return Tab(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        StickerWidget(
                          assetPath: pack.iconPath,
                          emoji: pack.stickers.isNotEmpty
                              ? pack.stickers.first.emoji ?? '📦'
                              : '📦',
                          name: pack.name,
                          size: 24,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          pack.name,
                          style: const TextStyle(fontSize: 10),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          // Sticker grid
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: _stickerPacks.map((pack) {
                return _buildStickerGrid(pack);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStickerGrid(VStickerPack pack) {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: pack.stickers.length,
      itemBuilder: (context, index) {
        final sticker = pack.stickers[index];
        return StickerWidget(
          assetPath: sticker.assetPath,
          emoji: sticker.emoji ?? '🎭',
          name: sticker.name,
          size: 64,
          onTap: () => widget.onStickerSelected(sticker),
        );
      },
    );
  }
}
