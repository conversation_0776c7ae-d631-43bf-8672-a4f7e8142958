// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/sticker_models.dart';

/// Widget for displaying network-loaded stickers with caching
class NetworkStickerWidget extends StatelessWidget {
  final VSticker sticker;
  final double size;
  final VoidCallback? onTap;
  final BoxFit fit;

  const NetworkStickerWidget({
    super.key,
    required this.sticker,
    this.size = 64.0,
    this.onTap,
    this.fit = BoxFit.contain,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: _buildStickerImage(),
        ),
      ),
    );
  }

  Widget _buildStickerImage() {
    // Check if it's a network URL or local asset
    if (sticker.assetPath.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: sticker.assetPath,
        width: size,
        height: size,
        fit: fit,
        placeholder: (context, url) => _buildPlaceholder(),
        errorWidget: (context, url, error) => _buildErrorWidget(),
        memCacheWidth: size.toInt() * 2, // 2x for retina displays
        memCacheHeight: size.toInt() * 2,
      );
    } else {
      // Local asset
      return Image.asset(
        sticker.assetPath,
        width: size,
        height: size,
        fit: fit,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
      );
    }
  }

  Widget _buildPlaceholder() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size * 0.3,
            height: size * 0.3,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Colors.grey.shade400,
            ),
          ),
          if (size > 48) // Only show text for larger sizes
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'Loading...',
                style: TextStyle(
                  fontSize: size * 0.08,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image,
            size: size * 0.4,
            color: Colors.grey.shade500,
          ),
          if (size > 48) // Only show text for larger sizes
            Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(
                'Failed',
                style: TextStyle(
                  fontSize: size * 0.08,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }
}

/// Widget for displaying stickers in chat messages
class ChatStickerWidget extends StatelessWidget {
  final VSticker sticker;
  final double size;
  final VoidCallback? onTap;

  const ChatStickerWidget({
    super.key,
    required this.sticker,
    this.size = 120.0,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: size,
          maxHeight: size,
        ),
        child: Hero(
          tag: 'sticker_${sticker.id}',
          child: NetworkStickerWidget(
            sticker: sticker,
            size: size,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}

/// Animated sticker widget with loading states
class AnimatedStickerWidget extends StatefulWidget {
  final VSticker sticker;
  final double size;
  final VoidCallback? onTap;

  const AnimatedStickerWidget({
    super.key,
    required this.sticker,
    this.size = 64.0,
    this.onTap,
  });

  @override
  State<AnimatedStickerWidget> createState() => _AnimatedStickerWidgetState();
}

class _AnimatedStickerWidgetState extends State<AnimatedStickerWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _controller.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
    widget.onTap?.call();
  }

  void _onTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: NetworkStickerWidget(
              sticker: widget.sticker,
              size: widget.size,
            ),
          );
        },
      ),
    );
  }
}
