// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';

/// A placeholder widget for stickers when actual image assets are not available
class StickerPlaceholder extends StatelessWidget {
  final String emoji;
  final String name;
  final double size;
  final VoidCallback? onTap;

  const StickerPlaceholder({
    super.key,
    required this.emoji,
    required this.name,
    this.size = 64.0,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              emoji,
              style: TextStyle(
                fontSize: size * 0.4,
              ),
            ),
            if (size > 48) // Only show name for larger sizes
              Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Text(
                  name,
                  style: TextStyle(
                    fontSize: size * 0.1,
                    color: Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// A widget that displays a sticker, falling back to placeholder if asset is not found
class StickerWidget extends StatelessWidget {
  final String assetPath;
  final String emoji;
  final String name;
  final double size;
  final VoidCallback? onTap;

  const StickerWidget({
    super.key,
    required this.assetPath,
    required this.emoji,
    required this.name,
    this.size = 64.0,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.asset(
            assetPath,
            width: size,
            height: size,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Fallback to placeholder if asset is not found
              return StickerPlaceholder(
                emoji: emoji,
                name: name,
                size: size,
                onTap: onTap,
              );
            },
          ),
        ),
      ),
    );
  }
}
