# Sticker Assets

This directory contains sticker assets for the chat input UI.

## Directory Structure

- `emotions/` - Emotion-based stickers (happy, sad, angry, etc.)
- `animals/` - Animal stickers (cat, dog, bear, etc.)
- `actions/` - Action stickers (thumbs up, clap, wave, etc.)

## File Format

Stickers should be in PNG format with transparent backgrounds.
Recommended size: 128x128 pixels for optimal display.

## Adding New Stickers

1. Add the image file to the appropriate category folder
2. Update the sticker data in `lib/src/data/sticker_data.dart`
3. Add the asset path to the package's `pubspec.yaml`

## Placeholder Files

Currently using placeholder files. Replace with actual sticker images:

### Emotions
- happy.png - Happy face sticker
- sad.png - Sad face sticker  
- angry.png - Angry face sticker
- love.png - Love/heart eyes sticker
- surprised.png - Surprised face sticker
- thinking.png - Thinking face sticker

### Animals
- cat.png - Cat sticker
- dog.png - Dog sticker
- bear.png - Bear sticker
- rabbit.png - Rabbit sticker

### Actions
- thumbs_up.png - Thumbs up sticker
- thumbs_down.png - Thumbs down sticker
- clap.png - Clapping hands sticker
- wave.png - Waving hand sticker
