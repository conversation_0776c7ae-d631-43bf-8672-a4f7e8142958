// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

class LocationMessageItem extends StatelessWidget {
  final VLocationMessage message;

  const LocationMessageItem({
    super.key,
    required this.message,
  });

  String _getStaticMapUrl() {
    final lat = message.data.latLng.latitude;
    final lng = message.data.latLng.longitude;
    const apiKey = SConstants.googleMapsApiKey;
    const zoom = 15;
    const size = '300x200';
    const mapType = 'roadmap';

    return 'https://maps.googleapis.com/maps/api/staticmap?'
        'center=$lat,$lng&'
        'zoom=$zoom&'
        'size=$size&'
        'maptype=$mapType&'
        'markers=color:red%7C$lat,$lng&'
        'key=$apiKey';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        await VStringUtils.lunchMap(
          latitude: message.data.latLng.latitude,
          longitude: message.data.latLng.longitude,
          title: message.data.linkPreviewData.title,
          description: message.data.linkPreviewData.description,
        );
      },
      child: Container(
        constraints: const BoxConstraints(maxWidth: 300),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: CupertinoColors.systemBackground,
        ),
        clipBehavior: Clip.hardEdge,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Map preview image
            SizedBox(
              height: 200,
              width: double.infinity,
              child: CachedNetworkImage(
                imageUrl: _getStaticMapUrl(),
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: CupertinoColors.systemGrey5,
                  child: const Center(
                    child: CupertinoActivityIndicator(),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: CupertinoColors.systemGrey5,
                  child: const Center(
                    child: Icon(
                      CupertinoIcons.location,
                      size: 40,
                      color: CupertinoColors.systemGrey,
                    ),
                  ),
                ),
              ),
            ),
            // Location details
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        CupertinoIcons.location_solid,
                        size: 16,
                        color: CupertinoColors.systemRed,
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          message.data.linkPreviewData.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: CupertinoColors.label,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message.data.linkPreviewData.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: CupertinoColors.secondaryLabel,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
