// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

enum SStorageKeys {
  myProfile,
  vAccessToken,
  isLogin,
  appTheme,
  lastAcceptedCall,
  clintVersion,
  adminAccessPassword,
  isViewer,
  isFirstRun,
  appRootPath,
  appMetaData,
  appLanguageTitle,
  inAppAlerts,
  appLanguageCode,
  lastAppliedUpdate,
  lastSuccessFetchRoomsTime,
  vBaseUrl,
  appConfigModelData,
  mobileDataMediaDownloadOptions,
  wifiMediaDownloadOptions,
}

enum RegisterStatus {
  accepted,
  pending,
  notAccepted,
}

enum UserPrivacyType { forReq, public, none }

enum StoryPrivacy { public, somePeople }

enum ApiI18nErrorRes {
  invalidLoginData,
  userEmailNotFound,
  yourAccountBlocked,
  yourAccountDeleted,
  userAlreadyRegister,
  codeHasBeenExpired,
  invalidCode,
  whileAuth<PERSON>an<PERSON><PERSON>Y<PERSON>,
  userRegisterStatusNotAcceptedYet,
  deviceHasBeenLogoutFromAllDevices,
  userDeviceSessionEndDeviceDeleted,
  noCodeHasBeenSendToYouToVerifyYourEmail,
  roomAlreadyInCall,
  peerUserInCallNow,
  callNotAllowed,
  peerUserDeviceOffline,
  emailMustBeValid,
  wait2MinutesToSendMail,
}

enum UserRoles { prime, admin, hasBadge, none }

enum ChatRequestStatus { accepted, refused, canceled, pending }

enum RegisterMethod { email, phone, apple, google, facebook }
