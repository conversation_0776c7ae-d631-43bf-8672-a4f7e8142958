import 'package:flutter/material.dart';

/// Widget to display sticker messages in chat
class StickerMessageWidget extends StatelessWidget {
  final bool isMeSender;
  final Map<String, dynamic> data;

  const StickerMessageWidget({
    super.key,
    required this.isMeSender,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    print('StickerMessageWidget build called with data: $data');

    final assetPath = data['assetPath'] as String?;
    final stickerName = data['name'] as String?;
    final emoji = data['emoji'] as String?;

    print(
        'Parsed sticker data - assetPath: $assetPath, name: $stickerName, emoji: $emoji');

    if (assetPath == null || stickerName == null) {
      // Fallback for invalid sticker data
      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          'Invalid sticker',
          style: TextStyle(
            color: Colors.red,
            fontSize: 14,
          ),
        ),
      );
    }

    // Check if this is a GIPHY sticker (network URL)
    final isNetworkSticker = assetPath.startsWith('http');

    return Container(
      constraints: const BoxConstraints(
        maxWidth: 120,
        maxHeight: 120,
      ),
      child: GestureDetector(
        onTap: () => _showStickerDetails(context),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: isNetworkSticker
                ? Image.network(
                    assetPath,
                    width: 100,
                    height: 100,
                    fit: BoxFit.contain,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Center(
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.grey.shade600,
                              size: 24,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Failed to load',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                : Image.asset(
                    assetPath,
                    width: 100,
                    height: 100,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              emoji ?? '🎭',
                              style: const TextStyle(fontSize: 32),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              stickerName,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ),
        ),
      ),
    );
  }

  void _showStickerDetails(BuildContext context) {
    final stickerName = data['name'] as String?;

    if (stickerName != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Sticker: $stickerName'),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }
}
