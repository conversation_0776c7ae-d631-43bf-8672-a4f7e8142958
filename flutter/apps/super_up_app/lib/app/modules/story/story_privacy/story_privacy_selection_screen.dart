import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../core/api_service/profile/profile_api_service.dart';
import '../../choose_members/views/choose_members_view.dart';

class StoryPrivacySelectionScreen extends StatefulWidget {
  final Function(StoryPrivacy privacy, List<String>? selectedUserIds)
      onPrivacySelected;

  const StoryPrivacySelectionScreen({
    super.key,
    required this.onPrivacySelected,
  });

  @override
  State<StoryPrivacySelectionScreen> createState() =>
      _StoryPrivacySelectionScreenState();
}

class _StoryPrivacySelectionScreenState
    extends State<StoryPrivacySelectionScreen> {
  StoryPrivacy _selectedPrivacy = StoryPrivacy.public;
  List<SBaseUser> _selectedUsers = [];
  final _profileApiService = GetIt.instance.get<ProfileApiService>();

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(S.of(context).yourStory),
        leading: CupertinoNavigationBarBackButton(
          onPressed: () => Navigator.of(context).pop(),
        ),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: _onDone,
          child: Text(
            S.of(context).done,
            style: const TextStyle(
              color: CupertinoColors.activeBlue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 20),
            Text(
              'Who can see your story?',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: CupertinoListSection(
                children: [
                  CupertinoListTile(
                    leading: const Icon(
                      CupertinoIcons.globe,
                      color: CupertinoColors.activeBlue,
                    ),
                    title: Text('Everyone'),
                    subtitle: Text('Share with all your contacts'),
                    trailing: _selectedPrivacy == StoryPrivacy.public
                        ? const Icon(
                            CupertinoIcons.check_mark_circled_solid,
                            color: CupertinoColors.activeBlue,
                          )
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedPrivacy = StoryPrivacy.public;
                        _selectedUsers.clear();
                      });
                    },
                  ),
                  CupertinoListTile(
                    leading: const Icon(
                      CupertinoIcons.person_2,
                      color: CupertinoColors.activeBlue,
                    ),
                    title: Text('Selected contacts'),
                    subtitle: _selectedUsers.isEmpty
                        ? Text('Choose specific contacts')
                        : Text('${_selectedUsers.length} contacts selected'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_selectedPrivacy == StoryPrivacy.somePeople)
                          const Icon(
                            CupertinoIcons.check_mark_circled_solid,
                            color: CupertinoColors.activeBlue,
                          ),
                        const SizedBox(width: 8),
                        const Icon(
                          CupertinoIcons.chevron_right,
                          color: CupertinoColors.systemGrey,
                          size: 16,
                        ),
                      ],
                    ),
                    onTap: _selectContacts,
                  ),
                ],
              ),
            ),
            if (_selectedUsers.isNotEmpty) ...[
              const Divider(),
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Selected contacts:',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 60,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _selectedUsers.length,
                        itemBuilder: (context, index) {
                          final user = _selectedUsers[index];
                          return Container(
                            margin: const EdgeInsets.only(right: 12),
                            child: Column(
                              children: [
                                VCircleAvatar(
                                  vFileSource: VPlatformFile.fromUrl(
                                    networkUrl: user.userImage,
                                  ),
                                  radius: 20,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  user.fullName.split(' ').first,
                                  style: const TextStyle(fontSize: 12),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _selectContacts() async {
    final selectedUsers = await Navigator.of(context).push<List<SBaseUser>>(
      CupertinoPageRoute(
        builder: (context) => ChooseMembersView(
          maxCount: 100, // Allow selecting up to 100 contacts
          onDone: (users) {
            Navigator.of(context).pop(users);
          },
          onCloseSheet: () {
            Navigator.of(context).pop();
          },
        ),
      ),
    );

    if (selectedUsers != null && selectedUsers.isNotEmpty) {
      setState(() {
        _selectedPrivacy = StoryPrivacy.somePeople;
        _selectedUsers = selectedUsers;
      });
    }
  }

  void _onDone() {
    List<String>? userIds;
    if (_selectedPrivacy == StoryPrivacy.somePeople &&
        _selectedUsers.isNotEmpty) {
      userIds = _selectedUsers.map((user) => user.id).toList();
    }

    widget.onPrivacySelected(_selectedPrivacy, userIds);
    Navigator.of(context).pop();
  }
}
