// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBC3o-5vj0c9zRxEdg4NzI1zeDUWCJofjA',
    appId: '1:507943861710:web:d27274ff625b3592733f9f',
    messagingSenderId: '507943861710',
    projectId: 'orbit-fc910',
    authDomain: 'orbit-fc910.firebaseapp.com',
    storageBucket: 'orbit-fc910.firebasestorage.app',
    measurementId: 'G-XJ9SK9FHJP',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBajx0oj_JPE3hEZ4DvTh1PmZvRptUe0Xo',
    appId: '1:507943861710:android:d6f427a595767d76733f9f',
    messagingSenderId: '507943861710',
    projectId: 'orbit-fc910',
    storageBucket: 'orbit-fc910.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDQfO8kRUJT5mHToU3gXBTpK0KQNIE6wx0',
    appId: '1:507943861710:ios:71461c9dbf8e17a1733f9f',
    messagingSenderId: '507943861710',
    projectId: 'orbit-fc910',
    storageBucket: 'orbit-fc910.firebasestorage.app',
    iosBundleId: 'com.orbit.ke',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDQfO8kRUJT5mHToU3gXBTpK0KQNIE6wx0',
    appId: '1:507943861710:ios:71461c9dbf8e17a1733f9f',
    messagingSenderId: '507943861710',
    projectId: 'orbit-fc910',
    storageBucket: 'orbit-fc910.firebasestorage.app',
    iosBundleId: 'com.orbit.ke',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBC3o-5vj0c9zRxEdg4NzI1zeDUWCJofjA',
    appId: '1:507943861710:web:73dfaf6dbbcab709733f9f',
    messagingSenderId: '507943861710',
    projectId: 'orbit-fc910',
    authDomain: 'orbit-fc910.firebaseapp.com',
    storageBucket: 'orbit-fc910.firebasestorage.app',
    measurementId: 'G-WYWGV1ZVVT',
  );

}